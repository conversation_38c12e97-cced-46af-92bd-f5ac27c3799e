import { db } from "@/lib/db";
import { 
  CreateWorkspaceInput, 
  UpdateWorkspaceInput, 
  WorkspaceQueryInput,
  BulkUpdateWorkspacesInput 
} from "@/lib/validations/workspace";

// Workspace CRUD operations
export const getWorkspacesByProject = async (projectId: string) => {
  try {
    const workspaces = await db.workspace.findMany({
      where: { projectId },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
        instances: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            instances: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
    return workspaces;
  } catch (error) {
    console.error("Error fetching workspaces:", error);
    return [];
  }
};

export const getWorkspacesByOrganization = async (organizationId: string, query?: WorkspaceQueryInput) => {
  try {
    const where: any = {
      project: { organizationId },
    };

    if (query?.status) {
      where.status = query.status;
    }

    if (query?.search) {
      where.OR = [
        { name: { contains: query.search, mode: "insensitive" } },
        { description: { contains: query.search, mode: "insensitive" } },
      ];
    }

    const [workspaces, total] = await Promise.all([
      db.workspace.findMany({
        where,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              organizationId: true,
            },
          },
          instances: {
            select: {
              id: true,
              name: true,
              status: true,
              createdAt: true,
            },
            orderBy: { createdAt: "desc" },
          },
          _count: {
            select: {
              instances: true,
            },
          },
        },
        orderBy: { [query?.sortBy || "updatedAt"]: query?.sortOrder || "desc" },
        skip: query?.page ? (query.page - 1) * (query.limit || 10) : 0,
        take: query?.limit || 10,
      }),
      db.workspace.count({ where }),
    ]);

    return {
      workspaces,
      total,
      page: query?.page || 1,
      limit: query?.limit || 10,
      totalPages: Math.ceil(total / (query?.limit || 10)),
    };
  } catch (error) {
    console.error("Error fetching workspaces:", error);
    return {
      workspaces: [],
      total: 0,
      page: 1,
      limit: 10,
      totalPages: 0,
    };
  }
};

export const getWorkspaceById = async (id: string) => {
  try {
    const workspace = await db.workspace.findUnique({
      where: { id },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            organizationId: true,
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        },
        instances: {
          include: {
            metrics: {
              orderBy: { timestamp: "desc" },
              take: 10,
            },
          },
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: {
            instances: true,
          },
        },
      },
    });
    return workspace;
  } catch (error) {
    console.error("Error fetching workspace:", error);
    return null;
  }
};

export const createWorkspace = async (data: CreateWorkspaceInput) => {
  try {
    const workspace = await db.workspace.create({
      data,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
        _count: {
          select: {
            instances: true,
          },
        },
      },
    });
    return workspace;
  } catch (error) {
    console.error("Error creating workspace:", error);
    throw new Error("Failed to create workspace");
  }
};

export const updateWorkspace = async (id: string, data: UpdateWorkspaceInput) => {
  try {
    const workspace = await db.workspace.update({
      where: { id },
      data,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
        _count: {
          select: {
            instances: true,
          },
        },
      },
    });
    return workspace;
  } catch (error) {
    console.error("Error updating workspace:", error);
    throw new Error("Failed to update workspace");
  }
};

export const deleteWorkspace = async (id: string) => {
  try {
    await db.workspace.delete({
      where: { id },
    });
    return true;
  } catch (error) {
    console.error("Error deleting workspace:", error);
    throw new Error("Failed to delete workspace");
  }
};

export const bulkUpdateWorkspaces = async (data: BulkUpdateWorkspacesInput) => {
  try {
    const result = await db.workspace.updateMany({
      where: {
        id: { in: data.workspaceIds },
      },
      data: data.updates,
    });
    return result;
  } catch (error) {
    console.error("Error bulk updating workspaces:", error);
    throw new Error("Failed to bulk update workspaces");
  }
};

// Workspace statistics
export const getWorkspaceStats = async (organizationId: string) => {
  try {
    const [
      totalWorkspaces,
      activeWorkspaces,
      inactiveWorkspaces,
      archivedWorkspaces,
      totalInstances,
      runningInstances,
    ] = await Promise.all([
      db.workspace.count({
        where: { project: { organizationId } },
      }),
      db.workspace.count({
        where: { 
          project: { organizationId },
          status: "ACTIVE",
        },
      }),
      db.workspace.count({
        where: { 
          project: { organizationId },
          status: "INACTIVE",
        },
      }),
      db.workspace.count({
        where: { 
          project: { organizationId },
          status: "ARCHIVED",
        },
      }),
      db.webVMInstance.count({
        where: { workspace: { project: { organizationId } } },
      }),
      db.webVMInstance.count({
        where: { 
          workspace: { project: { organizationId } },
          status: "RUNNING",
        },
      }),
    ]);

    return {
      totalWorkspaces,
      activeWorkspaces,
      inactiveWorkspaces,
      archivedWorkspaces,
      errorWorkspaces: totalWorkspaces - activeWorkspaces - inactiveWorkspaces - archivedWorkspaces,
      totalInstances,
      runningInstances,
      stoppedInstances: totalInstances - runningInstances,
    };
  } catch (error) {
    console.error("Error fetching workspace stats:", error);
    return {
      totalWorkspaces: 0,
      activeWorkspaces: 0,
      inactiveWorkspaces: 0,
      archivedWorkspaces: 0,
      errorWorkspaces: 0,
      totalInstances: 0,
      runningInstances: 0,
      stoppedInstances: 0,
    };
  }
};

export const getWorkspacesByStatus = async (organizationId: string, status: string) => {
  try {
    const workspaces = await db.workspace.findMany({
      where: { 
        project: { organizationId },
        status: status as any,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            instances: true,
          },
        },
      },
      orderBy: { updatedAt: "desc" },
    });
    return workspaces;
  } catch (error) {
    console.error("Error fetching workspaces by status:", error);
    return [];
  }
};

import { Suspense } from "react";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { DashboardOverview } from "@/components/dashboard/dashboard-overview";
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { DashboardHeader } from "@/components/dashboard/dashboard-header";

export default async function ManagementPage() {
  const session = await auth();
  
  if (!session?.user) {
    redirect("/auth/signin");
  }

  // For now, we'll use a default organization ID
  // In a real app, you'd get this from the user's current organization
  const organizationId = "default-org-id";

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Management Dashboard"
        text="Manage your projects, workspaces, and WebVM instances."
      />
      <Suspense fallback={<div>Loading dashboard...</div>}>
        <DashboardOverview organizationId={organizationId} />
      </Suspense>
    </DashboardShell>
  );
}

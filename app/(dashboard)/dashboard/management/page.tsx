import { Suspense } from "react";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { DashboardOverview } from "@/components/dashboard/dashboard-overview";
import { PageWrapper } from "@/components/dashboard/layout/page-wrapper";
import { Button } from "@/components/ui/button";
import { RefreshCw, Download, Settings } from "lucide-react";

export default async function ManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/auth/signin");
  }

  // For now, we'll use a default organization ID
  // In a real app, you'd get this from the user's current organization
  const organizationId = "default-org-id";

  return (
    <PageWrapper
      title="Management Dashboard"
      description="Manage your projects, workspaces, and WebVM instances."
      breadcrumbs={[
        { title: "Home", href: "/dashboard" },
        { title: "Management", isCurrentPage: true },
      ]}
      actions={
        <>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </>
      }
    >
      <Suspense fallback={<div>Loading dashboard...</div>}>
        <DashboardOverview organizationId={organizationId} />
      </Suspense>
    </PageWrapper>
  );
}

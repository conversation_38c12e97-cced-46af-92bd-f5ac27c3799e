import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { getProjectStats } from "@/lib/data/project";
import { getWorkspaceStats } from "@/lib/data/workspace";
import { getWebVMInstanceStats } from "@/lib/data/webvm-instance";
import { getUserOrganizationRole } from "@/lib/data/organization";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Check if user is a member of the organization
    const userRole = await getUserOrganizationRole(session.user.id, organizationId);
    if (!userRole) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Fetch all stats in parallel
    const [projectStats, workspaceStats, instanceStats] = await Promise.all([
      getProjectStats(organizationId),
      getWorkspaceStats(organizationId),
      getWebVMInstanceStats(organizationId),
    ]);

    const dashboardStats = {
      projects: projectStats,
      workspaces: workspaceStats,
      instances: instanceStats,
      overview: {
        totalProjects: projectStats.totalProjects,
        totalWorkspaces: workspaceStats.totalWorkspaces,
        totalInstances: instanceStats.totalInstances,
        activeWorkspaces: workspaceStats.activeWorkspaces,
        runningInstances: instanceStats.runningInstances,
        totalTasks: projectStats.totalTasks,
        completedTasks: projectStats.completedTasks,
        completionRate: projectStats.totalTasks > 0 
          ? Math.round((projectStats.completedTasks / projectStats.totalTasks) * 100) 
          : 0,
        instanceUtilization: instanceStats.totalInstances > 0 
          ? Math.round((instanceStats.runningInstances / instanceStats.totalInstances) * 100) 
          : 0,
      },
    };

    return NextResponse.json(dashboardStats);
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

"use client";

import { useState } from "react";
import {
  Package,
  Monitor,
  Server,
  Activity,
  TrendingUp,
  Users,
  Calendar,
  BarChart3
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useDashboardStats } from "@/hooks/use-dashboard";
import { ProjectsList } from "./projects/projects-list";
import { WorkspacesList } from "./workspaces/workspaces-list";
import { WebVMInstancesList } from "./webvm/webvm-instances-list";
import { CreateProjectDialog } from "./projects/create-project-dialog";
import { CreateWorkspaceDialog } from "./workspaces/create-workspace-dialog";
import { CreateWebVMDialog } from "./webvm/create-webvm-dialog";
import { DashboardFilters, FilterState } from "./management/dashboard-filters";

interface DashboardOverviewProps {
  organizationId: string;
}

export function DashboardOverview({ organizationId }: DashboardOverviewProps) {
  const { stats, overview, isLoading, isError } = useDashboardStats(organizationId);
  const [activeTab, setActiveTab] = useState("overview");
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    status: "",
    sortBy: "updatedAt",
    sortOrder: "desc",
  });

  const handleFiltersChange = (newFilters: FilterState) => {
    setFilters(newFilters);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError || !overview) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-destructive">Error loading dashboard</h3>
          <p className="text-sm text-muted-foreground mt-2">Failed to fetch dashboard statistics</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              {overview.completionRate}% completion rate
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Workspaces</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.activeWorkspaces}</div>
            <p className="text-xs text-muted-foreground">
              of {overview.totalWorkspaces} total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Running Instances</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.runningInstances}</div>
            <p className="text-xs text-muted-foreground">
              {overview.instanceUtilization}% utilization
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Tasks</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.completedTasks}</div>
            <p className="text-xs text-muted-foreground">
              of {overview.totalTasks} total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Project Progress</CardTitle>
            <CardDescription>Overall completion across all projects</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Completion Rate</span>
                <span>{overview.completionRate}%</span>
              </div>
              <Progress value={overview.completionRate} className="h-2" />
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{overview.completedTasks} completed</span>
              <span>{overview.totalTasks - overview.completedTasks} remaining</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Instance Utilization</CardTitle>
            <CardDescription>WebVM instances currently running</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Utilization Rate</span>
                <span>{overview.instanceUtilization}%</span>
              </div>
              <Progress value={overview.instanceUtilization} className="h-2" />
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{overview.runningInstances} running</span>
              <span>{overview.totalInstances - overview.runningInstances} stopped</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="projects">Projects</TabsTrigger>
              <TabsTrigger value="workspaces">Workspaces</TabsTrigger>
              <TabsTrigger value="instances">Instances</TabsTrigger>
            </TabsList>

            <div className="flex gap-2">
              {activeTab === "projects" && (
                <CreateProjectDialog organizationId={organizationId} />
              )}
              {activeTab === "workspaces" && (
                <CreateWorkspaceDialog organizationId={organizationId} />
              )}
              {activeTab === "instances" && (
                <CreateWebVMDialog organizationId={organizationId} />
              )}
            </div>
          </div>

          {activeTab !== "overview" && (
            <DashboardFilters
              activeTab={activeTab}
              onFiltersChange={handleFiltersChange}
            />
          )}
        </div>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Project</Badge>
                    <span className="text-muted-foreground">New project created</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Workspace</Badge>
                    <span className="text-muted-foreground">Workspace activated</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Instance</Badge>
                    <span className="text-muted-foreground">Instance started</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Workspaces</span>
                    <span>{overview.totalWorkspaces}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Instances</span>
                    <span>{overview.totalInstances}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Projects</span>
                    <span>{overview.totalProjects}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-base">System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Projects</span>
                    <Badge variant="default">Healthy</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Workspaces</span>
                    <Badge variant="default">Healthy</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Instances</span>
                    <Badge variant="default">Healthy</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="projects">
          <ProjectsList organizationId={organizationId} />
        </TabsContent>

        <TabsContent value="workspaces">
          <WorkspacesList
            organizationId={organizationId}
            query={{
              search: filters.search,
              status: filters.status as any,
              sortBy: filters.sortBy as any,
              sortOrder: filters.sortOrder,
              page: 1,
              limit: 20,
            }}
          />
        </TabsContent>

        <TabsContent value="instances">
          <WebVMInstancesList
            organizationId={organizationId}
            query={{
              search: filters.search,
              status: filters.status as any,
              sortBy: filters.sortBy as any,
              sortOrder: filters.sortOrder,
              page: 1,
              limit: 20,
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  LayoutDashboard,
  Package,
  Monitor,
  Server,
  Settings,
  Users,
  BarChart3,
  FolderOpen,
  Activity,
  ChevronDown,
  ChevronRight,
  Home,
  Layers,
  Database,
  Terminal,
  GitBranch,
  Cloud,
  Zap,
  Shield,
  HelpCircle,
  Menu,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface SidebarProps {
  className?: string;
  isCollapsed?: boolean;
  onToggle?: () => void;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    title: "Overview",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "Management",
    href: "/dashboard/management",
    icon: LayoutDashboard,
    badge: "New",
  },
  {
    title: "Projects",
    icon: Package,
    children: [
      {
        title: "All Projects",
        href: "/dashboard/projects",
        icon: FolderOpen,
      },
      {
        title: "Create Project",
        href: "/dashboard/projects/new",
        icon: Package,
      },
    ],
  },
  {
    title: "Workspaces",
    icon: Monitor,
    children: [
      {
        title: "All Workspaces",
        href: "/dashboard/workspaces",
        icon: Layers,
      },
      {
        title: "Active Workspaces",
        href: "/dashboard/workspaces?status=active",
        icon: Activity,
      },
    ],
  },
  {
    title: "WebVM Instances",
    icon: Server,
    children: [
      {
        title: "All Instances",
        href: "/dashboard/instances",
        icon: Database,
      },
      {
        title: "Running Instances",
        href: "/dashboard/instances?status=running",
        icon: Zap,
      },
      {
        title: "Instance Metrics",
        href: "/dashboard/instances/metrics",
        icon: BarChart3,
      },
    ],
  },
  {
    title: "WebVM Workspace",
    href: "/dashboard/webvm",
    icon: Terminal,
  },
];

const bottomNavItems: NavItem[] = [
  {
    title: "Team",
    href: "/dashboard/team",
    icon: Users,
  },
  {
    title: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
  },
  {
    title: "Security",
    href: "/dashboard/security",
    icon: Shield,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Help & Support",
    href: "/dashboard/help",
    icon: HelpCircle,
  },
];

export function DashboardSidebar({ className, isCollapsed = false, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const [openItems, setOpenItems] = useState<string[]>([]);

  const toggleItem = (title: string) => {
    setOpenItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const isItemActive = (href?: string) => {
    if (!href) return false;
    return pathname === href || pathname.startsWith(href + '/');
  };

  const hasActiveChild = (children?: NavItem[]) => {
    if (!children) return false;
    return children.some(child => isItemActive(child.href));
  };

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openItems.includes(item.title);
    const isActive = isItemActive(item.href);
    const hasActiveChildren = hasActiveChild(item.children);

    if (hasChildren) {
      return (
        <Collapsible
          key={item.title}
          open={isOpen}
          onOpenChange={() => toggleItem(item.title)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start h-9 px-3",
                level > 0 && "ml-4 w-[calc(100%-1rem)]",
                (isActive || hasActiveChildren) && "bg-accent text-accent-foreground",
                isCollapsed && "justify-center px-2"
              )}
            >
              <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
              {!isCollapsed && (
                <>
                  <span className="flex-1 text-left">{item.title}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="ml-auto text-xs">
                      {item.badge}
                    </Badge>
                  )}
                  {isOpen ? (
                    <ChevronDown className="h-4 w-4 ml-1" />
                  ) : (
                    <ChevronRight className="h-4 w-4 ml-1" />
                  )}
                </>
              )}
            </Button>
          </CollapsibleTrigger>
          {!isCollapsed && (
            <CollapsibleContent className="space-y-1">
              {item.children?.map(child => renderNavItem(child, level + 1))}
            </CollapsibleContent>
          )}
        </Collapsible>
      );
    }

    return (
      <Button
        key={item.title}
        variant="ghost"
        className={cn(
          "w-full justify-start h-9 px-3",
          level > 0 && "ml-4 w-[calc(100%-1rem)]",
          isActive && "bg-accent text-accent-foreground",
          isCollapsed && "justify-center px-2"
        )}
        asChild
      >
        <Link href={item.href || "#"}>
          <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
          {!isCollapsed && (
            <>
              <span className="flex-1 text-left">{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto text-xs">
                  {item.badge}
                </Badge>
              )}
            </>
          )}
        </Link>
      </Button>
    );
  };

  return (
    <div className={cn(
      "flex flex-col border-r bg-background",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Header */}
      <div className="flex h-16 items-center border-b px-4">
        {!isCollapsed ? (
          <Link href="/dashboard" className="flex items-center gap-2">
            <div className="h-8 w-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">VK</span>
            </div>
            <span className="font-bold text-xl">VibeKraft</span>
          </Link>
        ) : (
          <Link href="/dashboard" className="flex items-center justify-center w-full">
            <div className="h-8 w-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">VK</span>
            </div>
          </Link>
        )}
        
        {onToggle && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className={cn("ml-auto", isCollapsed && "ml-0")}
          >
            {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-1">
          {navigationItems.map(item => renderNavItem(item))}
        </div>
        
        {!isCollapsed && (
          <>
            <Separator className="my-4" />
            <div className="space-y-1">
              {bottomNavItems.map(item => renderNavItem(item))}
            </div>
          </>
        )}
      </ScrollArea>

      {/* Footer */}
      {!isCollapsed && (
        <div className="border-t p-4">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Cloud className="h-3 w-3" />
            <span>Connected to Cloud</span>
            <div className="ml-auto h-2 w-2 bg-green-500 rounded-full"></div>
          </div>
        </div>
      )}
    </div>
  );
}
